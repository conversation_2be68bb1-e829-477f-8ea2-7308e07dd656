defmodule Drops.Relation.Schema do
  @moduledoc """
  Represents comprehensive schema metadata for a database table/relation.

  This struct stores all extracted metadata about a database table including
  primary keys, foreign keys, field information, and indices. It serves as
  a central container for schema information that can be used for validation,
  documentation, and code generation.

  ## Examples

      # Create a schema with metadata
      schema = %Drops.Relation.Schema{
        source: "users",
        primary_key: %Drops.Relation.Schema.PrimaryKey{fields: [:id]},
        foreign_keys: [],
        fields: [
          %{name: :id, type: :integer, ecto_type: :id, source: :id},
          %{name: :email, type: :string, ecto_type: :string, source: :email}
        ],
        indices: %Drops.Relation.Schema.Indices{indices: [...]},
        associations: [
          # Ecto association structs (BelongsTo, Has, ManyToMany, etc.)
        ],
        virtual_fields: []
      }
  """

  alias Drops.Relation.Schema.{PrimaryKey, ForeignKey, Indices, Field}

  @type field_metadata :: %{
          name: atom(),
          type: atom(),
          ecto_type: term(),
          source: atom()
        }

  @type t :: %__MODULE__{
          source: String.t(),
          primary_key: PrimaryKey.t(),
          foreign_keys: [ForeignKey.t()],
          fields: [Field.t()],
          indices: Indices.t(),
          associations: [
            Ecto.Association.BelongsTo.t()
            | Ecto.Association.Has.t()
            | Ecto.Association.ManyToMany.t()
          ],
          virtual_fields: [atom()]
        }

  defstruct [
    :source,
    :primary_key,
    :foreign_keys,
    :fields,
    :indices,
    :associations,
    :virtual_fields
  ]

  @doc """
  Creates a new Schema struct with the provided metadata.

  ## Parameters

  - `source` - The table name
  - `primary_key` - Primary key information
  - `foreign_keys` - List of foreign key relationships
  - `fields` - List of field metadata
  - `indices` - Index information
  - `associations` - List of Ecto association structs
  - `virtual_fields` - List of virtual field names

  ## Examples

      iex> pk = Drops.Relation.Schema.PrimaryKey.new([:id])
      iex> indices = Drops.Relation.Schema.Indices.new([])
      iex> schema = Drops.Relation.Schema.new("users", pk, [], [], indices, [], [])
      iex> schema.source
      "users"
  """
  @spec new(
          String.t(),
          PrimaryKey.t(),
          [ForeignKey.t()],
          [Field.t()],
          Indices.t(),
          [
            Ecto.Association.BelongsTo.t()
            | Ecto.Association.Has.t()
            | Ecto.Association.ManyToMany.t()
          ],
          [atom()]
        ) :: t()
  def new(
        source,
        primary_key,
        foreign_keys,
        fields,
        indices,
        associations,
        virtual_fields
      ) do
    %__MODULE__{
      source: source,
      primary_key: primary_key,
      foreign_keys: foreign_keys,
      fields: fields,
      indices: indices,
      associations: associations,
      virtual_fields: virtual_fields
    }
  end

  @doc """
  Creates a Schema struct from an Ecto schema module.

  This function uses the MetadataExtractor to gather all available metadata
  from the Ecto schema and optionally from the database.

  ## Parameters

  - `schema_module` - The Ecto schema module
  - `repo` - The Ecto repository (optional, required for index introspection)

  ## Examples

      iex> schema = Drops.Relation.Schema.from_ecto_schema(MyApp.User, MyApp.Repo)
      iex> schema.source
      "users"
  """
  @spec from_ecto_schema(module(), module() | nil) :: t()
  def from_ecto_schema(schema_module, repo \\ nil) when is_atom(schema_module) do
    alias Drops.Relation.Schema.MetadataExtractor

    metadata = MetadataExtractor.extract_metadata(schema_module, repo)

    %__MODULE__{
      source: metadata.source,
      primary_key: metadata.primary_key,
      foreign_keys: metadata.foreign_keys,
      fields: metadata.fields,
      indices: metadata.indices,
      associations: metadata.associations,
      virtual_fields: metadata.virtual_fields
    }
  end

  @doc """
  Finds a field by name in the schema.

  ## Examples

      iex> field = Drops.Relation.Schema.find_field(schema, :email)
      iex> field.name
      :email
  """
  @spec find_field(t(), atom()) :: Field.t() | nil
  def find_field(%__MODULE__{fields: fields}, field_name) when is_atom(field_name) do
    Enum.find(fields, &Field.matches_name?(&1, field_name))
  end

  @doc """
  Checks if a field is a primary key field.

  ## Examples

      iex> Drops.Relation.Schema.primary_key_field?(schema, :id)
      true
  """
  @spec primary_key_field?(t(), atom()) :: boolean()
  def primary_key_field?(%__MODULE__{primary_key: primary_key}, field_name)
      when is_atom(field_name) do
    field_name in PrimaryKey.field_names(primary_key)
  end

  @doc """
  Checks if a field is a foreign key field.

  ## Examples

      iex> Drops.Relation.Schema.foreign_key_field?(schema, :user_id)
      true
  """
  @spec foreign_key_field?(t(), atom()) :: boolean()
  def foreign_key_field?(%__MODULE__{foreign_keys: foreign_keys}, field_name)
      when is_atom(field_name) do
    Enum.any?(foreign_keys, &(&1.field == field_name))
  end

  @doc """
  Gets the foreign key information for a specific field.

  ## Examples

      iex> fk = Drops.Relation.Schema.get_foreign_key(schema, :user_id)
      iex> fk.references_table
      "users"
  """
  @spec get_foreign_key(t(), atom()) :: ForeignKey.t() | nil
  def get_foreign_key(%__MODULE__{foreign_keys: foreign_keys}, field_name)
      when is_atom(field_name) do
    Enum.find(foreign_keys, &(&1.field == field_name))
  end

  @doc """
  Checks if the schema has a composite primary key.

  ## Examples

      iex> Drops.Relation.Schema.composite_primary_key?(schema)
      false
  """
  @spec composite_primary_key?(t()) :: boolean()
  def composite_primary_key?(%__MODULE__{primary_key: primary_key}) do
    PrimaryKey.composite?(primary_key)
  end

  @doc """
  Gets all field names in the schema.

  ## Examples

      iex> Drops.Relation.Schema.field_names(schema)
      [:id, :name, :email, :created_at, :updated_at]
  """
  @spec field_names(t()) :: [atom()]
  def field_names(%__MODULE__{fields: fields}) do
    Enum.map(fields, & &1.name)
  end

  @doc """
  Gets all foreign key field names in the schema.

  ## Examples

      iex> Drops.Relation.Schema.foreign_key_field_names(schema)
      [:user_id, :category_id]
  """
  @spec foreign_key_field_names(t()) :: [atom()]
  def foreign_key_field_names(%__MODULE__{foreign_keys: foreign_keys}) do
    Enum.map(foreign_keys, & &1.field)
  end

  @doc """
  Finds an association by field name in the schema.

  ## Examples

      iex> assoc = Drops.Relation.Schema.find_association(schema, :user)
      iex> assoc.field
      :user
  """
  @spec find_association(t(), atom()) ::
          Ecto.Association.BelongsTo.t()
          | Ecto.Association.Has.t()
          | Ecto.Association.ManyToMany.t()
          | nil
  def find_association(%__MODULE__{associations: associations}, field_name)
      when is_atom(field_name) do
    Enum.find(associations, &(&1.field == field_name))
  end

  @doc """
  Gets all association field names in the schema.

  ## Examples

      iex> Drops.Relation.Schema.association_field_names(schema)
      [:user, :comments, :tags]
  """
  @spec association_field_names(t()) :: [atom()]
  def association_field_names(%__MODULE__{associations: associations}) do
    Enum.map(associations, & &1.field)
  end

  @doc """
  Filters associations by type (belongs_to, has_many, has_one, many_to_many).

  ## Examples

      iex> belongs_to_assocs = Drops.Relation.Schema.associations_by_type(schema, :belongs_to)
      iex> length(belongs_to_assocs)
      2
  """
  @spec associations_by_type(t(), :belongs_to | :has_many | :has_one | :many_to_many) :: [
          Ecto.Association.BelongsTo.t()
          | Ecto.Association.Has.t()
          | Ecto.Association.ManyToMany.t()
        ]
  def associations_by_type(%__MODULE__{associations: associations}, type) do
    case type do
      :belongs_to ->
        Enum.filter(associations, &match?(%Ecto.Association.BelongsTo{}, &1))

      :has_many ->
        Enum.filter(
          associations,
          &(match?(%Ecto.Association.Has{}, &1) and &1.cardinality == :many)
        )

      :has_one ->
        Enum.filter(
          associations,
          &(match?(%Ecto.Association.Has{}, &1) and &1.cardinality == :one)
        )

      :many_to_many ->
        Enum.filter(associations, &match?(%Ecto.Association.ManyToMany{}, &1))
    end
  end

  @doc """
  Gets the source table name for the schema.

  ## Examples

      iex> Drops.Relation.Schema.source_table(schema)
      "users"
  """
  @spec source_table(t()) :: String.t()
  def source_table(%__MODULE__{source: source}) do
    source
  end
end
