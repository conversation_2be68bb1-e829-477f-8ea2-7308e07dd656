defmodule Drops.Relation do
  defmacro __using__(opts) do
    quote do
      use Ecto.Schema

      import Drops.Relation

      @before_compile Drops.Relation

      @opts unquote(opts)
    end
  end

  defmacro associations(do: block) do
    quote do
      @associations unquote(Macro.escape(block))
    end
  end

  defmacro __before_compile__(env) do
    relation = env.module

    opts = Module.get_attribute(relation, :opts)
    repo = opts[:repo]
    name = opts[:name]

    {ecto_schema, drops_schema} =
      Drops.Relation.SchemaCache.get_or_infer_schema(repo, name, fn ->
        Drops.Relation.Inference.infer_schema(relation, name, repo)
      end)

    quote do
      require unquote(repo)

      unquote(ecto_schema)

      # Extract associations from the compiled Ecto schema and update the Drops schema
      @after_compile __MODULE__

      # Store the inferred Drops.Relation.Schema as a module attribute
      @schema unquote(Macro.escape(drops_schema))

      @doc """
      Returns the inferred Drops.Relation.Schema for this relation.

      The schema contains comprehensive metadata including primary keys,
      foreign keys, field information, indices, and associations.

      ## Examples

          iex> MyRelation.schema()
          %Drops.Relation.Schema{
            source: "my_table",
            primary_key: %Drops.Relation.Schema.PrimaryKey{fields: [:id]},
            foreign_keys: [...],
            fields: [...],
            indices: %Drops.Relation.Schema.Indices{...},
            associations: [...],
            virtual_fields: [...]
          }
      """
      @spec schema() :: Drops.Relation.Schema.t()
      def schema do
        # Extract associations from the compiled Ecto schema
        associations =
          if function_exported?(__MODULE__, :__schema__, 1) do
            Drops.Relation.Schema.MetadataExtractor.extract_associations(__MODULE__)
          else
            []
          end

        # Update the schema with extracted associations
        %{@schema | associations: associations}
      end

      def __after_compile__(_env, _bytecode) do
        # This callback is called after the module is compiled
        # We could use this for additional processing if needed
        :ok
      end
    end
  end

  defmodule Inference do
    alias Drops.Relation.Schema.{PrimaryKey, Indices, Field}

    def infer_schema(relation, name, repo) do
      # Introspect table columns and types using SQLite PRAGMA
      columns = introspect_table_columns(repo, name)

      # Generate Ecto schema fields from column information
      field_definitions = generate_field_definitions(columns)

      # Get optional Ecto associations definitions AST
      association_definitions = Module.get_attribute(relation, :associations, [])

      # Create the Ecto schema AST
      ecto_schema =
        quote do
          schema unquote(name) do
            (unquote_splicing(field_definitions))

            unquote(association_definitions)
          end
        end

      # Create the Drops.Relation.Schema from the introspected data
      drops_schema = create_drops_schema(relation, name, columns, repo)

      {ecto_schema, drops_schema}
    end

    defp create_drops_schema(_relation, table_name, columns, repo) do
      alias Drops.Relation.Schema.{PrimaryKey, Indices}

      # Extract primary key fields from columns
      primary_key_fields =
        columns
        |> Enum.filter(& &1.primary_key)
        |> Enum.map(&String.to_atom(&1.name))
        |> case do
          # Default Ecto primary key
          [] -> [:id]
          fields -> fields
        end

      # Convert columns to Field structs
      fields =
        columns
        |> Enum.reject(fn column ->
          # Skip timestamp fields and primary key named 'id' (Ecto adds this automatically)
          column.name in ["inserted_at", "updated_at"] or
            (column.primary_key and column.name == "id")
        end)
        |> Enum.map(fn column ->
          field_name = String.to_atom(column.name)
          ecto_type = sqlite_type_to_ecto_type(column.type, field_name)

          Field.new(
            field_name,
            normalize_ecto_type(ecto_type),
            ecto_type,
            field_name
          )
        end)

      # Add the default :id field that Ecto adds
      id_field = Field.new(:id, :integer, :id, :id)

      all_fields = [id_field | fields]

      # Create primary key with proper Field structs
      primary_key_field_structs =
        Enum.filter(all_fields, fn field ->
          field.name in primary_key_fields
        end)

      primary_key = PrimaryKey.new(primary_key_field_structs)

      # Extract indices from database if repo is provided
      indices =
        if repo do
          case extract_indices_from_db(repo, table_name) do
            {:ok, indices} -> indices
            {:error, _} -> Indices.new()
          end
        else
          Indices.new()
        end

      # Create the schema struct
      Drops.Relation.Schema.new(
        table_name,
        primary_key,
        # foreign_keys - cannot be inferred from database structure alone
        [],
        all_fields,
        indices,
        # associations - cannot be inferred from database structure alone
        [],
        # virtual_fields - cannot be inferred from database structure alone
        []
      )
    end

    defp extract_indices_from_db(repo, table_name) do
      # Use the existing DatabaseIntrospector if available
      try do
        # DatabaseIntrospector.get_table_indices already returns {:ok, indices} or {:error, reason}
        Drops.Relation.Schema.DatabaseIntrospector.get_table_indices(repo, table_name)
      rescue
        _ -> {:error, :introspection_failed}
      end
    end

    defp normalize_ecto_type(ecto_type) do
      case ecto_type do
        :id -> :integer
        :binary_id -> :binary
        other -> other
      end
    end

    defp introspect_table_columns(repo, table_name) do
      # Use SQLite PRAGMA table_info to get column information
      query = "PRAGMA table_info(#{table_name})"

      case repo.query(query) do
        {:ok, %{rows: rows, columns: _columns}} ->
          # PRAGMA table_info returns: [cid, name, type, notnull, dflt_value, pk]
          Enum.map(rows, fn [_cid, name, type, notnull, _dflt_value, pk] ->
            %{
              name: name,
              type: type,
              not_null: notnull == 1,
              primary_key: pk == 1
            }
          end)

        {:error, error} ->
          raise "Failed to introspect table #{table_name}: #{inspect(error)}"
      end
    end

    defp generate_field_definitions(columns) do
      columns
      |> Enum.reject(fn column ->
        # Skip timestamp fields and primary key named 'id' (Ecto adds this automatically)
        column.name in ["inserted_at", "updated_at"] or
          (column.primary_key and column.name == "id")
      end)
      |> Enum.map(fn column ->
        field_name = String.to_atom(column.name)
        ecto_type = sqlite_type_to_ecto_type(column.type, field_name)

        # Regular field (primary key 'id' is handled automatically by Ecto)
        quote do
          field(unquote(field_name), unquote(ecto_type))
        end
      end)
    end

    defp sqlite_type_to_ecto_type(sqlite_type, field_name) do
      case String.upcase(sqlite_type) do
        "INTEGER" ->
          # If field name ends with _id, it's likely a foreign key
          if field_name && String.ends_with?(Atom.to_string(field_name), "_id") do
            :id
          else
            :integer
          end

        "TEXT" ->
          :string

        "REAL" ->
          :float

        "BLOB" ->
          :binary

        "DATETIME" ->
          :naive_datetime

        "DATE" ->
          :date

        "TIME" ->
          :time

        "BOOLEAN" ->
          :boolean

        "JSON" ->
          :map

        # Default to string for unknown types
        _ ->
          :string
      end
    end
  end
end
