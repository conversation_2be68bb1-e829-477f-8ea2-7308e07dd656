defmodule Drops.Relations.AssociationsTest do
  use Drops.OperationCase, async: true

  describe "associations" do
    @tag ecto_schemas: [Test.Ecto.UserGroupSchemas.User, Test.Ecto.UserGroupSchemas.Group]
    test "sets up regular Ecto associations" do
      defmodule Test.Users do
        use Drops.Relation, repo: Drops.TestRepo, name: "users", infer: true

        associations do
          many_to_many(:groups, Test.Ecto.UserGroupSchemas.Group,
            join_through: "user_groups"
          )
        end
      end

      defmodule Test.Groups do
        use Drops.Relation, repo: Drops.TestRepo, name: "groups", infer: true

        associations do
          many_to_many(:users, Test.Ecto.UserGroupSchemas.User,
            join_through: "user_groups"
          )
        end
      end

      # Check that associations are properly set up as Ecto association structs
      schema = Test.Users.schema()
      assert length(schema.associations) > 0

      # Find the groups association
      groups_assoc = Enum.find(schema.associations, &(&1.field == :groups))
      assert groups_assoc != nil
      assert groups_assoc.__struct__ == Ecto.Association.ManyToMany
    end
  end
end
